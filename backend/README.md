# Auth Tutorial - Refactored & Upgraded 🔒

A modern, secure full-stack authentication application built with **React 19** and **Node.js**, featuring email-based authentication with **<PERSON><PERSON>mailer**.

![Demo App](/frontend/public/screenshot-for-readme.png)

## 🚀 Features

- **Modern React 19** - Latest React version with improved performance
- **Email Authentication** - Secure email-based registration and login using Nodemailer
- **Email Verification** - Account verification via email codes
- **Password Reset** - Secure password reset via email links
- **JWT Authentication** - Secure token-based authentication
- **Rate Limiting** - Protection against brute force attacks
- **Input Validation** - Comprehensive client and server-side validation
- **Error Handling** - User-friendly error messages and logging
- **Security Headers** - Helmet.js for security best practices
- **Environment Configuration** - Secure configuration management
- **Responsive Design** - Mobile-friendly UI with Tailwind CSS

## 🛠 Tech Stack

### Frontend
- **React 19** - Latest React version
- **Vite 7** - Fast build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animation library
- **Zustand** - State management
- **React Router** - Client-side routing
- **React Hot Toast** - Toast notifications
- **Axios** - HTTP client

### Backend
- **Node.js** - JavaScript runtime
- **Express.js** - Web framework
- **MongoDB** - NoSQL database
- **Mongoose** - MongoDB ODM
- **Nodemailer** - Email service (replaces Mailtrap)
- **JWT** - JSON Web Tokens
- **bcryptjs** - Password hashing
- **Helmet** - Security middleware
- **Express Rate Limit** - Rate limiting
- **Validator** - Input validation

## 🔧 Recent Upgrades & Refactoring

- ✅ **React upgraded** from 18.3.1 to 19.1.1
- ✅ **Replaced Mailtrap with Nodemailer** for email services
- ✅ **Added comprehensive validation** on both client and server
- ✅ **Implemented rate limiting** for security
- ✅ **Enhanced error handling** with user-friendly messages
- ✅ **Added security middleware** (Helmet, sanitization)
- ✅ **Environment configuration** for both frontend and backend
- ✅ **Improved code structure** following best practices

### Setup .env file

```bash
MONGO_URI=your_mongo_uri
PORT=5000
JWT_SECRET=your_secret_key
NODE_ENV=development

MAILTRAP_TOKEN=your_mailtrap_token
MAILTRAP_ENDPOINT=https://send.api.mailtrap.io/

CLIENT_URL= http://localhost:5173
```

### Run this app locally

```shell
npm run build
```

### Start the app

```shell
npm run start
```

### I'll see you in the next one! 🚀
