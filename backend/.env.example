# Backend Environment Variables

# Server Configuration
NODE_ENV=development
PORT=5000

# Database Configuration
MONGO_URI=mongodb://localhost:27017/auth_tutorial

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random

# Client URL (for password reset links)
CLIENT_URL=http://localhost:5173

# Email Configuration (Nodemailer)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password_here
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Your App Name

# Alternative SMTP providers (uncomment to use)
# For Outlook/Hotmail
# EMAIL_HOST=smtp-mail.outlook.com
# EMAIL_PORT=587

# For Yahoo
# EMAIL_HOST=smtp.mail.yahoo.com
# EMAIL_PORT=587

# For custom SMTP
# EMAIL_HOST=your_smtp_host
# EMAIL_PORT=587
# EMAIL_SECURE=false
